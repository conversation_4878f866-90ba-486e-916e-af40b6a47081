"error_network" = "Network connection lost. Please try again.";
"error_service" = "Service temporarily unavailable. Try again later.";
"error_invalid_input" = "Please check your input and try again.";
"error_pantry_empty" = "Your pantry is empty.";
"error_pantry" = "Pantry error. Please try again.";
"error_configuration" = "Configuration error: %@";
"error_unknown" = "Something went wrong.";


// UI - Modes
"mode_selector_title" = "Mode";
"mode_quick" = "Quick";
"mode_custom" = "Custom";
"a11y_mode_selector_label" = "Mode";
"a11y_mode_selector_hint" = "Switch between Quick and Custom.";

// UI - Generate Button
"quick_generate_cta" = "Quick Dinner";
"action_cancel" = "Cancel";
"a11y_generate" = "Generate";
"a11y_cancel_generation" = "Cancel generation";
"a11y_generate_hint" = "Generate recipe suggestions.";

// UI - Results
"results_idle_quick" = "Tap 'Quick Dinner' to generate suggestions.";
"results_loading" = "Generating...";
"action_retry" = "Retry";
"a11y_results_idle" = "No results";
"a11y_results_loading" = "Loading";
"a11y_results_loaded" = "Results";
"a11y_results_failed" = "Failed";

// UI - RecipeCard
"ingredients_from_pantry_prefix" = "From your pantry:";
"additional_ingredients_prefix" = "You may need:";
"a11y_recipe_card_hint" = "Opens recipe details.";


// UI - Configuration Summary
"configuration_summary" = "Configuration summary";
"configuration_summary_hint" = "Summary of your current plan";
"summary_day_part_format" = "for %d day%s";
"summary_meals_none" = "(please select meals)";
"summary_meals_joiner" = " and ";
"summary_full_format" = "You are planning a %@ %@ plan.";

/* Meal Types */
"meal_breakfast" = "Breakfast";
"meal_lunch" = "Lunch";
"meal_dinner" = "Dinner";

/* Cooking Styles */
"style_quick_meal" = "Quick Meal";
"style_kid_friendly" = "Kid Friendly";
"style_weekend_feast" = "Weekend Feast";
"style_healthy" = "Healthy";
"style_comfort" = "Comfort";

/* Cuisines */
"cuisine_homestyle" = "Homestyle";
"cuisine_asian" = "Asian";
"cuisine_mediterranean" = "Mediterranean";
"cuisine_mexican" = "Mexican";
"cuisine_italian" = "Italian";

/* Legacy ingredient strategies removed per Phase 1 */

/* Enhanced Configuration Summary */
"config_summary_empty" = "Please select at least one meal to get started...";
"config_summary_one_day" = "1 day";
"config_summary_days" = "%d days";
"config_summary_planning" = "Planning %@ of %@";
/* Legacy style and strategy summary strings removed per Phase 1 */

// UI - Meal Plan Start Date
"start_date_label" = "Start Date";
"start_date_helper" = "Start generating from this date.";

/* Pantry State Messages */
"pantry_empty_warning" = "Your pantry is empty. Please add ingredients to generate recipes.";
"pantry_empty_action" = "Go to Pantry";
"pantry_loading" = "Checking your pantry...";
"pantry_error" = "Unable to access your pantry. Please try again.";

/* Date Strings */
"date_today" = "Today";
"date_tomorrow" = "Tomorrow";
