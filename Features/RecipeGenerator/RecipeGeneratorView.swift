import SwiftUI

struct RecipeGeneratorView: View {
    @State private var viewModel = RecipeGeneratorViewModel(
        recipeService: ServiceContainer.shared.recipeGenerationService,
        authService: ServiceContainer.shared.authenticationService
    )
    @Environment(PantryService.self) var pantryService: PantryService
    @Environment(AuthenticationService.self) var authService: AuthenticationService
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    @State private var showError = false
    @State private var showSaveToast = false

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Pantry state warning (enhanced)
                if viewModel.pantryState.isEmpty {
                    emptyPantryView
                }

                // Recipe generation content (always show, but disable when pantry empty)
                recipeGenerationContent
                    .padding()
                    .disabled(viewModel.pantryState.isEmpty)
                    .opacity(viewModel.pantryState.isEmpty ? 0.6 : 1.0)
            }
        }
        .navigationTitle("Recipe Ideas")
        .navigationBarTitleDisplayMode(.large)
        .onAppear { applyGeneratorPrefillIfNeeded() }
        .alert("Recipe Generation Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            if case .failed(let displayError) = viewModel.viewState {
                Text(displayError.errorDescription ?? "Unknown")
            } else {
                Text("Unknown")
            }
        }
        .overlay {
            if case .preview(let items) = viewModel.toastState {
                GeometryReader { proxy in
                    ZStack {
                        // Backdrop
                        Color.black.opacity(0.25)
                            .ignoresSafeArea()
                            .transition(.opacity)
                            .accessibilityHidden(true)

                        // Centered toast card (75% width, auto height)
                        GeneratorToastView(
                            mealType: viewModel.quickConfiguration.mealType,
                            items: items,
                            onGood: { viewModel.acceptQuickPreview() },
                            onRegenerate: { viewModel.dismissQuickPreview() }
                        )
                        .frame(maxWidth: min(480, proxy.size.width * 0.75))
                        .transition(AnyTransition.scale.combined(with: AnyTransition.opacity))
                    }
                    .frame(width: proxy.size.width, height: proxy.size.height, alignment: .center)
                    .accessibilityAddTraits(.isModal)
                }
            }
        }
        // Save confirmation toast (C3.3)
        .toast(
            message: viewModel.saveToastMessage ?? "",
            type: .success,
            duration: 2.5,
            isPresented: $showSaveToast
        )
        .onChange(of: viewModel.saveToastMessage) { _, newValue in
            if let msg = newValue, !msg.isEmpty {
                // Trigger toast presentation
                withAnimation { showSaveToast = true }
            }
        }
        .onChange(of: showSaveToast) { old, new in
            // Clear message after toast hides to avoid re-show loops
            if old == true && new == false {
                viewModel.saveToastMessage = nil
            }
        }
        // Capacity full notice for Quick history
        .alert("Quick Recipes Full", isPresented: Binding(
            get: { viewModel.capacityAlert != nil },
            set: { if !$0 { viewModel.capacityAlert = nil } }
        )) {
            Button("Manage") { coordinator.switchToTab(3) }
            Button("OK", role: .cancel) { }
        } message: {
            Text(viewModel.capacityAlert ?? "")
        }
    }

    // MARK: - Prefill (from Recipes empty states)
    private func applyGeneratorPrefillIfNeeded() {
        let defaults = UserDefaults.standard
        guard let modeRaw = defaults.string(forKey: "generator.prefill.mode") else { return }

        if modeRaw == UIMode.custom.rawValue {
            // Switch to custom and apply days + meals
            if viewModel.mode != .custom { viewModel.selectMode(.custom) }
            let days = defaults.integer(forKey: "generator.prefill.days")
            if days > 0 { viewModel.customConfiguration.days = days }
            if let mealsRaw = defaults.array(forKey: "generator.prefill.meals") as? [String] {
                let meals = Set(mealsRaw.compactMap { MealType(rawValue: $0) })
                if !meals.isEmpty { viewModel.customConfiguration.selectedMeals = meals }
            }
        } else if modeRaw == UIMode.quick.rawValue {
            if viewModel.mode != .quick { viewModel.selectMode(.quick) }
        }

        // Clear after applying to avoid repeated application
        defaults.removeObject(forKey: "generator.prefill.mode")
        defaults.removeObject(forKey: "generator.prefill.days")
        defaults.removeObject(forKey: "generator.prefill.meals")
    }

    private var emptyPantryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "basket")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            Text("Your Pantry is Empty")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Add some ingredients to your pantry to get personalized recipe suggestions.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Go to Pantry") {
                // Switch to pantry tab instead of using navigateTo
                coordinator.switchToPantryTab()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }

    private var recipeGenerationContent: some View {
        // Inject coordinator into view model for auto switch
        viewModel.navigationCoordinator = coordinator

        return VStack(spacing: 24) {
            // Mode selector
            ModeSelector(selectedMode: $viewModel.mode) { newMode in
                viewModel.selectMode(newMode)
            }

            // Quick controls
            if viewModel.mode == .quick {
                quickControls
            }

            // Meal Prep controls when in custom mode
            if viewModel.mode == .custom {
                // Days picker
                VStack(alignment: .leading, spacing: 8) {
                    Text("Days").font(.headline)
                    Picker("Days", selection: $viewModel.customConfiguration.days) {
                        ForEach(1...RemoteConfigurationManager.shared.configuration.maxDays, id: \.self) { d in
                            Text("\(d)").tag(d)
                        }
                    }
                    .pickerStyle(.segmented)
                    .frame(minHeight: 44)
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

                VStack(alignment: .leading, spacing: 8) {
                    DatePicker(
                        "start_date_label",
                        selection: $viewModel.userSelectedStartDate,
                        in: startDateRange,
                        displayedComponents: [.date]
                    )
                    .datePickerStyle(.compact)

                    Text("start_date_helper")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

                // Meal selector
                VStack(alignment: .leading, spacing: 8) {
                    Text("Meals").font(.headline)
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 72), spacing: 8)], spacing: 8) {
                        ForEach(MealType.allCases, id: \.self) { meal in
                            Toggle(isOn: Binding(
                                get: { viewModel.customConfiguration.selectedMeals.contains(meal) },
                                set: { isOn in
                                    if isOn {
                                        viewModel.customConfiguration.selectedMeals.insert(meal)
                                        if viewModel.customConfiguration.mealConfigurations[meal] == nil {
                                            viewModel.customConfiguration.mealConfigurations[meal] = MealConfig()
                                        }
                                    } else {
                                        viewModel.customConfiguration.selectedMeals.remove(meal)
                                        viewModel.customConfiguration.mealConfigurations.removeValue(forKey: meal)
                                    }
                                    Haptics.light()
                                }
                            )) {
                                Text(meal.displayName)
                                    .padding(.vertical, 8)
                                    .frame(maxWidth: .infinity)
                            }
                            .toggleStyle(.button)
                            .buttonBorderShape(.capsule)
                            .tint(.accentColor)
                            .frame(minHeight: 44)
                        }
                    }
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

                // Per-meal configs
                ForEach(Array(viewModel.customConfiguration.selectedMeals).sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { meal in
                    let binding = Binding<MealConfig>(
                        get: { viewModel.customConfiguration.mealConfigurations[meal] ?? MealConfig() },
                        set: { viewModel.customConfiguration.mealConfigurations[meal] = $0 }
                    )
                    MealConfigEditor(meal: meal, config: binding)
                }

                // Cuisines selector
                VStack(alignment: .leading, spacing: 8) {
                    Text("Cuisines").font(.headline)
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 96), spacing: 8)], spacing: 8) {
                        ForEach(RemoteConfigurationManager.shared.configuration.availableCuisines, id: \.self) { cuisine in
                            let isSelected = viewModel.customConfiguration.cuisines.contains(cuisine)
                            Toggle(isOn: Binding(
                                get: { isSelected },
                                set: { on in
                                    if on {
                                        if !viewModel.customConfiguration.cuisines.contains(cuisine) {
                                            viewModel.customConfiguration.cuisines.append(cuisine)
                                        }
                                    } else {
                                        viewModel.customConfiguration.cuisines.removeAll { $0 == cuisine }
                                    }
                                    Haptics.light()
                                }
                            )) {
                                Text(cuisine)
                                    .padding(.vertical, 8)
                                    .frame(maxWidth: .infinity)
                            }
                            .toggleStyle(.button)
                            .buttonBorderShape(.capsule)
                            .tint(.accentColor)
                            .frame(minHeight: 40)
                        }
                    }
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

                // Additional request
                VStack(alignment: .leading, spacing: 8) {
                    Text("Additional request").font(.headline)
                    TextEditor(text: $viewModel.customConfiguration.additionalRequest)
                        .frame(minHeight: 80)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(.secondary.opacity(0.2), lineWidth: 1)
                        )
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

                // Configuration Summary
                let mealsList = Array(viewModel.customConfiguration.selectedMeals)
                    .sorted(by: { $0.rawValue < $1.rawValue })
                    .map { $0.displayName }
                    .joined(separator: ", ")
                let summary = "Planning \(viewModel.customConfiguration.days) day(s) of \(!mealsList.isEmpty ? mealsList : "—")"
                ConfigurationSummaryCard(summary: summary)
            }

            // Generate recipes button
            GenerateButton(
                isLoading: {
                    if case .loading = viewModel.viewState { return true } else { return false }
                }(),
                isEnabled: viewModel.canGenerate,
                onGenerate: {
                    let time = viewModel.mode == .quick ? viewModel.quickConfiguration.totalTimeMinutes : 30
                    Task { await viewModel.generateRecipeIdeas(cookingTimeMinutes: time) }
                },
                onCancel: { viewModel.cancelGeneration() }
            )

            // Results removed per V4: inputs only in Generator tab. Results are shown in Recipes tab after generation.
        }
    }

    private var startDateRange: ClosedRange<Date> {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let maxDate = calendar.date(byAdding: .day, value: 7, to: today) ?? today
        return today...maxDate
    }

    @ViewBuilder
    private var resultsSection: some View {
        switch viewModel.viewState {
        case .idle:
            EmptyView()
        case .loading:
            ProgressView("Generating Ideas...")
                .frame(maxWidth: .infinity)
        case .loaded(let uiModels):
            VStack(alignment: .leading, spacing: 12) {
                Text("Recipe Ideas").font(.headline)
                LazyVStack(spacing: 12) {
                    ForEach(uiModels, id: \.id) { item in
                        Button(action: {
                            let recipe = Recipe(
                                recipeTitle: item.title,
                                description: item.subtitle ?? "",
                                ingredients: item.ingredientsFromPantry ?? [],
                                instructions: ["Follow steps to cook \(item.title)."],
                                nutrition: .init(calories: "—", protein: "—", carbs: "—", fat: "—"),
                                cookingTime: "\(item.estimatedTime ?? 30) minutes",
                                servings: 2,
                                difficulty: .easy
                            )
                            coordinator.navigateToRecipeDetail(recipe: recipe)
                        }) {
                            HStack {
                                VStack(alignment: .leading) {
                                    Text(item.title).font(.headline)
                                    if let sub = item.subtitle { Text(sub).font(.subheadline).foregroundColor(.secondary) }
                                }
                                Spacer()
                                Image(systemName: "chevron.right").foregroundColor(.secondary)
                            }
                            .padding()
                            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
                        }
                        .buttonStyle(.plain)
                    }
                }
            }
        case .failed:
            Color.clear
                .onAppear { showError = true }
        }
    }

    private var quickControls: some View {
        VStack(spacing: 12) {
            // Meal Type
            VStack(alignment: .leading, spacing: 8) {
                Text("Meal Type").font(.headline)
                Picker("Meal", selection: $viewModel.quickConfiguration.mealType) {
                    ForEach(MealType.allCases, id: \.self) { m in
                        Text(m.displayName).tag(m)
                    }
                }
                .pickerStyle(.segmented)
            }
            .padding()
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

            // Dishes & Time
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Dishes").font(.subheadline).foregroundStyle(.secondary)
                    Stepper(value: $viewModel.quickConfiguration.numberOfDishes, in: 1...6) {
                        Text("\(viewModel.quickConfiguration.numberOfDishes)")
                    }
                }
                Divider()
                VStack(alignment: .leading, spacing: 6) {
                    Text("Total Time (min)").font(.subheadline).foregroundStyle(.secondary)
                    Slider(value: Binding(
                        get: { Double(viewModel.quickConfiguration.totalTimeMinutes) },
                        set: { viewModel.quickConfiguration.totalTimeMinutes = Int($0) }
                    ), in: 5...120, step: 5) { Text("") } minimumValueLabel: { Text("5") } maximumValueLabel: { Text("120") }
                    Text("\(viewModel.quickConfiguration.totalTimeMinutes) min").font(.caption).foregroundStyle(.secondary)
                }
            }
            .padding()
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))

            // Cuisines chips
            CuisinesChips(selected: $viewModel.quickConfiguration.cuisines)

            // Additional Request
            AdditionalRequestEditor(text: $viewModel.quickConfiguration.additionalRequest)
        }
    }
}

// MARK: - Inline Toast Views (to avoid Xcode target file wiring issues)

private struct GeneratorToastView: View {
    let mealType: MealType
    let items: [RecipeUIModel]
    let onGood: () -> Void
    let onRegenerate: () -> Void

    var body: some View {
        VStack(spacing: 14) {
            // Header
            HStack(alignment: .center) {
                Image(systemName: "sparkles")
                    .foregroundStyle(.yellow)
                Text("Preview • " + mealType.displayName)
                    .font(.headline.weight(.semibold))
                Spacer()
            }
            .accessibilityLabel("Preview for " + mealType.displayName)

            // Preview content
            ToastPreviewView(items: items)

            // Actions
            HStack(spacing: 8) {
                Button(action: onRegenerate) {
                    Text("Regenerate")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
                .accessibilityLabel("Regenerate suggestions")

                Button(action: onGood) {
                    Label("Good", systemImage: "checkmark.circle.fill")
                        .labelStyle(.titleAndIcon)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .tint(.accentColor)
                .controlSize(.large)
                .accessibilityIdentifier("generator_toast_good")
                .accessibilityLabel("Save to Quick and view")
            }
        }
        .padding(16)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.secondary.opacity(0.15), lineWidth: 1)
        )
        .shadow(radius: 10)
        .padding(20)
        .transition(AnyTransition.scale.combined(with: AnyTransition.opacity))
        .accessibilityElement(children: .contain)
    }
}

private struct ToastPreviewView: View {
    let items: [RecipeUIModel]

    private var topItems: [RecipeUIModel] { Array(items.prefix(3)) }

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            ForEach(topItems, id: \.id) { item in
                HStack(alignment: .firstTextBaseline, spacing: 10) {
                    Image(systemName: "fork.knife")
                        .foregroundStyle(.secondary)
                    VStack(alignment: .leading, spacing: 2) {
                        Text(item.title)
                            .font(.subheadline)
                            .lineLimit(1)
                        if let t = item.estimatedTime {
                            Text("\(t) min")
                                .font(.caption2)
                                .foregroundStyle(.secondary)
                        }
                    }
                    Spacer()
                }
                .accessibilityElement(children: .ignore)
                .accessibilityLabel({ () -> String in
                    if let t = item.estimatedTime {
                        return "\(item.title), cooking time \(t) minutes"
                    } else {
                        return item.title
                    }
                }())
            }
            if items.count > 3 {
                Text("+\(items.count - 3) more")
                    .font(.caption2)
                    .foregroundStyle(.secondary)
                    .accessibilityLabel("Plus \(items.count - 3) more recipes")
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(Color(.secondarySystemBackground), in: RoundedRectangle(cornerRadius: 8))
    }
}

/*
// Grouped results view (moved to Results/GroupedResultsView.swift) — duplicate removed
    let items: [RecipeUIModel]
    let days: Int
    let selectedMeals: Set<MealType>

    var body: some View {
        let sections = group(items, days: days, selectedMeals: selectedMeals)
        List {
            ForEach(sections) { day in
                Section(day.displayDate) {
                    ForEach(day.meals) { meal in
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(meal.displayName).font(.headline)
                                Spacer()
                                Label("\(meal.totalTime) min", systemImage: "clock").font(.caption).foregroundStyle(.secondary)
                            }
                            LazyVStack(spacing: 8) {
                                ForEach(meal.dishes, id: \.id) { item in
                                    RecipeItemCard(item: item)
                                }
                            }
                            HStack(spacing: 12) {
                                ProgressView(value: meal.pantryUsage.utilizationRate)
                                    .tint(.green)
                                Text(String(format: "%.0f%% pantry", meal.pantryUsage.utilizationRate * 100))
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }
                        }
                        .listRowInsets(EdgeInsets())
                    }
                }
            }
        }
        .listStyle(.insetGrouped)
    }

    // MARK: - Types & grouping inside view
    struct DaySection: Identifiable {
        let id = UUID()
        let date: String
        let displayDate: String
        let meals: [MealSection]
    }
    struct MealSection: Identifiable {
        let id = UUID()
        let mealType: MealType
        let displayName: String
        let dishes: [RecipeUIModel]
        let totalTime: Int
        let pantryUsage: PantryUsage
    }
    struct PantryUsage {
        let itemsUsed: Int
        let itemsTotal: Int
        let utilizationRate: Double
    }

    private func group(_ items: [RecipeUIModel], days: Int, selectedMeals: Set<MealType>) -> [DaySection] {
        let days = max(1, days)
        let meals = selectedMeals.isEmpty ? Set(MealType.allCases) : selectedMeals
        var buckets: [[MealType: [RecipeUIModel]]] = Array(repeating: [:], count: days)
        var idx = 0
        for item in items {
            let dayIndex = idx % days
            let meal = meals.sorted { $0.rawValue < $1.rawValue }[idx % meals.count]
            buckets[dayIndex][meal, default: []].append(item)
            idx += 1
        }
        var sections: [DaySection] = []
        let today = Date()
        let formatter = DateFormatter(); formatter.dateFormat = "yyyy-MM-dd"
        let disp = DateFormatter(); disp.dateStyle = .medium
        for i in 0..<days {
            let date = Calendar.current.date(byAdding: .day, value: i, to: today) ?? today
            let dayStr = formatter.string(from: date)
            let displayStr = i == 0 ? "Today" : (i == 1 ? "Tomorrow" : disp.string(from: date))
            var mealSections: [MealSection] = []
            for meal in meals.sorted(by: { $0.rawValue < $1.rawValue }) {
                let dishes = buckets[i][meal] ?? []
                guard !dishes.isEmpty else { continue }
                let totalTime = dishes.compactMap { $0.estimatedTime }.reduce(0, +)
                let used = dishes.compactMap { $0.ingredientsFromPantry?.count }.reduce(0, +)
                let add = dishes.compactMap { $0.additionalIngredients?.count }.reduce(0, +)
                let total = used + add
                let util = total > 0 ? Double(used) / Double(total) : 0.0
                let usage = PantryUsage(itemsUsed: used, itemsTotal: total, utilizationRate: util)
                mealSections.append(MealSection(mealType: meal, displayName: meal.displayName, dishes: dishes, totalTime: totalTime, pantryUsage: usage))
            }
            sections.append(DaySection(date: dayStr, displayDate: displayStr, meals: mealSections))
        }
        return sections
    }
*/

// Simple Recipe Card View for Recipe objects
struct RecipeCard: View {
    let recipe: Recipe
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recipe.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        Text(recipe.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                HStack {
                    Label("\(recipe.cookingTimeInMinutes) min", systemImage: "clock")
                    Spacer()
                    Label("\(recipe.numberOfServings) servings", systemImage: "person.2")
                    Spacer()
                    Label(recipe.difficulty.rawValue, systemImage: "star")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Custom editors moved to dedicated files (Views/Custom/*.swift)


// FeedbackBar moved to Views/Feedback/FeedbackBar.swift

#Preview {
    NavigationStack { RecipeGeneratorView() }
        .environment(PantryService())
        .environment(AuthenticationService())
        .environment(NavigationCoordinator())
}

// CuisinesChips and FlowLayout moved to Views/Custom/PreferencesPicker.swift
