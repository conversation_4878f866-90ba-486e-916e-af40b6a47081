import Foundation

struct TimeoutError: Error {}

func withTimeout<T>(
    _ seconds: TimeInterval,
    operation: @escaping @Sendable () async throws -> T
) async throws -> T {
    let clampedSeconds = min(max(seconds, 0.001), 600.0)
    let nanoseconds = UInt64(clampedSeconds * 1_000_000_000)
    return try await withThrowingTaskGroup(of: T.self) { group in
        group.addTask { try await operation() }
        group.addTask {
            try await Task.sleep(nanoseconds: nanoseconds)
            throw TimeoutError()
        }
        let result = try await group.next()!
        group.cancelAll()
        return result
    }
}
